import { MaterialIcons } from '@expo/vector-icons';
import { useRouter } from 'expo-router';
import React, { useState } from 'react';
import { KeyboardAvoidingView, Platform, ScrollView, StyleSheet, View } from 'react-native';
import {
  Button,
  Card,
  Divider,
  Snackbar,
  Text,
  TextInput
} from 'react-native-paper';
import { useAuth } from '../contexts/AuthContext';
import { useAppTheme } from '../contexts/ThemeContext';
import { LoginRequest, SignupRequest, SSOProvider } from '../types';

type AuthMode = 'login' | 'signup';

export default function LoginScreen() {
  const [mode, setMode] = useState<AuthMode>('login');
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [displayName, setDisplayName] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [snackbarVisible, setSnackbarVisible] = useState(false);
  const [snackbarMessage, setSnackbarMessage] = useState('');

  const { login, signup, loginWithSSO, resetPassword, error, clearError } = useAuth();
  const { isDark } = useAppTheme();
  const theme = useAppTheme();
  const router = useRouter();

  const validateEmail = (email: string) => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  };

  const validateForm = () => {
    if (!email.trim()) {
      showSnackbar('Please enter your email address');
      return false;
    }

    if (!validateEmail(email)) {
      showSnackbar('Please enter a valid email address');
      return false;
    }

    if (!password.trim()) {
      showSnackbar('Please enter your password');
      return false;
    }

    if (mode === 'signup') {
      if (!displayName.trim()) {
        showSnackbar('Please enter your name');
        return false;
      }

      if (password.length < 6) {
        showSnackbar('Password must be at least 6 characters long');
        return false;
      }

      if (password !== confirmPassword) {
        showSnackbar('Passwords do not match');
        return false;
      }
    }

    return true;
  };

  const showSnackbar = (message: string) => {
    setSnackbarMessage(message);
    setSnackbarVisible(true);
  };

  const handleSubmit = async () => {
    if (!validateForm()) return;

    try {
      setIsLoading(true);
      clearError();

      if (mode === 'login') {
        const loginRequest: LoginRequest = { email: email.trim(), password };
        await login(loginRequest);
        router.replace('/(tabs)/avatars');
      } else {
        const signupRequest: SignupRequest = { 
          email: email.trim(), 
          password, 
          displayName: displayName.trim() 
        };
        await signup(signupRequest);
        router.replace('/(tabs)/avatars');
      }
    } catch (error: any) {
      // Error is handled by AuthContext and displayed via error state
    } finally {
      setIsLoading(false);
    }
  };

  const handleSSOLogin = async (provider: SSOProvider) => {
    try {
      setIsLoading(true);
      clearError();
      await loginWithSSO(provider);
      router.replace('/(tabs)/avatars');
    } catch (error: any) {
      showSnackbar(`${provider} login is not yet implemented`);
    } finally {
      setIsLoading(false);
    }
  };

  const handleForgotPassword = async () => {
    if (!email.trim()) {
      showSnackbar('Please enter your email address first');
      return;
    }

    if (!validateEmail(email)) {
      showSnackbar('Please enter a valid email address');
      return;
    }

    try {
      await resetPassword(email.trim());
      showSnackbar('Password reset email sent! Check your inbox.');
    } catch (error: any) {
      // Error handled by AuthContext
    }
  };

  const toggleMode = () => {
    setMode(mode === 'login' ? 'signup' : 'login');
    clearError();
    setPassword('');
    setConfirmPassword('');
    setDisplayName('');
  };

  return (
    <KeyboardAvoidingView 
      style={styles.container} 
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
    >
      <ScrollView 
        contentContainerStyle={styles.scrollContainer}
        keyboardShouldPersistTaps="handled"
      >
        <View style={styles.header}>
          <MaterialIcons 
            name="face" 
            size={80} 
            color={theme.colors.primary} 
          />
          <Text variant="headlineMedium" style={styles.title}>
            Characterise
          </Text>
          <Text variant="bodyLarge" style={styles.subtitle}>
            Create amazing AI avatars
          </Text>
        </View>

        <Card style={styles.card}>
          <Card.Content style={styles.cardContent}>
            <Text variant="headlineSmall" style={styles.formTitle}>
              {mode === 'login' ? 'Welcome Back' : 'Create Account'}
            </Text>

            {mode === 'signup' && (
              <TextInput
                label="Full Name"
                value={displayName}
                onChangeText={setDisplayName}
                mode="outlined"
                style={styles.input}
                left={<TextInput.Icon icon="account" />}
                autoCapitalize="words"
                autoComplete="name"
              />
            )}

            <TextInput
              label="Email"
              value={email}
              onChangeText={setEmail}
              mode="outlined"
              style={styles.input}
              left={<TextInput.Icon icon="email" />}
              keyboardType="email-address"
              autoCapitalize="none"
              autoComplete="email"
            />

            <TextInput
              label="Password"
              value={password}
              onChangeText={setPassword}
              mode="outlined"
              style={styles.input}
              left={<TextInput.Icon icon="lock" />}
              right={
                <TextInput.Icon 
                  icon={showPassword ? "eye-off" : "eye"} 
                  onPress={() => setShowPassword(!showPassword)}
                />
              }
              secureTextEntry={!showPassword}
              autoComplete="password"
            />

            {mode === 'signup' && (
              <TextInput
                label="Confirm Password"
                value={confirmPassword}
                onChangeText={setConfirmPassword}
                mode="outlined"
                style={styles.input}
                left={<TextInput.Icon icon="lock-check" />}
                secureTextEntry={!showPassword}
                autoComplete="password"
              />
            )}

            {error && (
              <Text style={[styles.errorText, { color: theme.colors.error }]}>
                {error.message}
              </Text>
            )}

            <Button
              mode="contained"
              onPress={handleSubmit}
              style={styles.submitButton}
              disabled={isLoading}
              loading={isLoading}
            >
              {mode === 'login' ? 'Sign In' : 'Create Account'}
            </Button>

            {mode === 'login' && (
              <Button
                mode="text"
                onPress={handleForgotPassword}
                style={styles.forgotButton}
              >
                Forgot Password?
              </Button>
            )}

            <Divider style={styles.divider} />

            <Text variant="bodyMedium" style={styles.ssoTitle}>
              Or continue with
            </Text>

            <View style={styles.ssoContainer}>
              <Button
                mode="outlined"
                onPress={() => handleSSOLogin('google')}
                style={styles.ssoButton}
                icon="google"
                disabled={isLoading}
              >
                Google
              </Button>

              <Button
                mode="outlined"
                onPress={() => handleSSOLogin('facebook')}
                style={styles.ssoButton}
                icon="facebook"
                disabled={isLoading}
              >
                Facebook
              </Button>
            </View>

            <View style={styles.switchModeContainer}>
              <Text variant="bodyMedium">
                {mode === 'login' ? "Don't have an account? " : "Already have an account? "}
              </Text>
              <Button mode="text" onPress={toggleMode} compact>
                {mode === 'login' ? 'Sign Up' : 'Sign In'}
              </Button>
            </View>
          </Card.Content>
        </Card>
      </ScrollView>

      <Snackbar
        visible={snackbarVisible}
        onDismiss={() => setSnackbarVisible(false)}
        duration={4000}
      >
        {snackbarMessage}
      </Snackbar>
    </KeyboardAvoidingView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollContainer: {
    flexGrow: 1,
    justifyContent: 'center',
    padding: 20,
  },
  header: {
    alignItems: 'center',
    marginBottom: 40,
  },
  title: {
    marginTop: 16,
    fontWeight: 'bold',
  },
  subtitle: {
    marginTop: 8,
    opacity: 0.7,
    textAlign: 'center',
  },
  card: {
    elevation: 4,
  },
  cardContent: {
    padding: 24,
  },
  formTitle: {
    textAlign: 'center',
    marginBottom: 24,
    fontWeight: 'bold',
  },
  input: {
    marginBottom: 16,
  },
  errorText: {
    marginBottom: 16,
    textAlign: 'center',
  },
  submitButton: {
    marginTop: 8,
    marginBottom: 16,
  },
  forgotButton: {
    marginBottom: 16,
  },
  divider: {
    marginVertical: 20,
  },
  ssoTitle: {
    textAlign: 'center',
    marginBottom: 16,
    opacity: 0.7,
  },
  ssoContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 24,
    gap: 12,
  },
  ssoButton: {
    flex: 1,
  },
  switchModeContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
  },
});
