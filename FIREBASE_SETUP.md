# Firebase Authentication Setup Guide

This guide will help you set up Firebase Authentication for the Characterise app.

## 1. Create a Firebase Project

1. Go to the [Firebase Console](https://console.firebase.google.com/)
2. Click "Create a project" or "Add project"
3. Enter your project name (e.g., "characterise-app")
4. Choose whether to enable Google Analytics (optional)
5. Click "Create project"

## 2. Enable Authentication

1. In your Firebase project console, click on "Authentication" in the left sidebar
2. Click on the "Get started" button
3. Go to the "Sign-in method" tab
4. Enable the following sign-in providers:
   - **Email/Password**: Click on it and toggle "Enable"
   - **Google**: Click on it, toggle "Enable", and add your app's support email
   - **Facebook**: Click on it, toggle "Enable", and add your Facebook App ID and App Secret

## 3. Get Your Firebase Configuration

1. Click on the gear icon (Project settings) in the left sidebar
2. Scroll down to "Your apps" section
3. Click on the web app icon (`</>`) to add a web app
4. Register your app with a nickname (e.g., "characterise-web")
5. Copy the Firebase configuration object

## 4. Update the Firebase Config

Replace the placeholder configuration in `config/firebase.ts` with your actual Firebase config:

```typescript
const firebaseConfig = {
  apiKey: "your-actual-api-key",
  authDomain: "your-project.firebaseapp.com",
  projectId: "your-actual-project-id",
  storageBucket: "your-project.appspot.com",
  messagingSenderId: "123456789012",
  appId: "your-actual-app-id"
};
```

## 5. Configure OAuth Providers (Optional)

### Google Sign-In
1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Select your project or create a new one
3. Enable the Google+ API
4. Create OAuth 2.0 credentials
5. Add your domain to authorized domains

### Facebook Sign-In
1. Go to [Facebook Developers](https://developers.facebook.com/)
2. Create a new app or use an existing one
3. Add Facebook Login product
4. Configure OAuth redirect URIs
5. Get your App ID and App Secret

## 6. Test the Authentication

1. Start your Expo development server: `npx expo start`
2. Navigate to the login screen
3. Try creating a new account with email/password
4. Try signing in with the created account
5. Test the logout functionality

## 7. Security Rules (Optional)

If you plan to use Firestore or Firebase Storage, make sure to set up proper security rules to protect user data.

## Troubleshooting

### Common Issues:

1. **"Firebase: Error (auth/configuration-not-found)"**
   - Make sure you've replaced the placeholder config with your actual Firebase config

2. **"Firebase: Error (auth/invalid-api-key)"**
   - Double-check your API key in the Firebase config

3. **SSO not working**
   - Make sure you've properly configured OAuth providers in Firebase Console
   - For React Native, additional setup with expo-auth-session might be required

4. **"Firebase: Error (auth/network-request-failed)"**
   - Check your internet connection
   - Make sure Firebase project is active

## Next Steps

Once authentication is working:
1. Consider implementing email verification
2. Add password reset functionality
3. Set up proper error handling
4. Implement user profile management
5. Add social login providers if needed

For production deployment, make sure to:
1. Set up proper Firebase security rules
2. Configure authorized domains
3. Enable App Check for additional security
4. Monitor authentication usage in Firebase Console
