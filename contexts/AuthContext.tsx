import auth, { FirebaseAuthTypes } from '@react-native-firebase/auth';
import React, { createContext, useContext, useEffect, useState } from 'react';
import { AuthError, AuthState, AuthUser, LoginRequest, SignupRequest, SSOProvider } from '../types';

interface AuthContextType extends AuthState {
  login: (request: LoginRequest) => Promise<void>;
  signup: (request: SignupRequest) => Promise<void>;
  logout: () => Promise<void>;
  loginWithSSO: (provider: SSOProvider) => Promise<void>;
  resetPassword: (email: string) => Promise<void>;
  updateUserProfile: (displayName: string, photoURL?: string) => Promise<void>;
  changePassword: (currentPassword: string, newPassword: string) => Promise<void>;
  error: AuthError | null;
  clearError: () => void;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

interface AuthProviderProps {
  children: React.ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [user, setUser] = useState<AuthUser | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<AuthError | null>(null);

  const isAuthenticated = !!user;

  // Convert Firebase User to AuthUser
  const convertFirebaseUser = (firebaseUser: FirebaseAuthTypes.User): AuthUser => ({
    uid: firebaseUser.uid,
    email: firebaseUser.email,
    displayName: firebaseUser.displayName,
    photoURL: firebaseUser.photoURL,
    emailVerified: firebaseUser.emailVerified,
  });

  // Handle authentication errors
  const handleAuthError = (error: any): AuthError => {
    let message = 'An unexpected error occurred';
    
    switch (error.code) {
      case 'auth/user-not-found':
        message = 'No account found with this email address';
        break;
      case 'auth/wrong-password':
        message = 'Incorrect password';
        break;
      case 'auth/email-already-in-use':
        message = 'An account with this email already exists';
        break;
      case 'auth/weak-password':
        message = 'Password should be at least 6 characters';
        break;
      case 'auth/invalid-email':
        message = 'Please enter a valid email address';
        break;
      case 'auth/too-many-requests':
        message = 'Too many failed attempts. Please try again later';
        break;
      case 'auth/network-request-failed':
        message = 'Network error. Please check your connection';
        break;
      default:
        message = error.message || message;
    }

    return { code: error.code, message };
  };

  const clearError = () => setError(null);

  const login = async (request: LoginRequest) => {
    try {
      setIsLoading(true);
      setError(null);
      const userCredential = await auth().signInWithEmailAndPassword(request.email, request.password);
      setUser(convertFirebaseUser(userCredential.user));
    } catch (error: any) {
      const authError = handleAuthError(error);
      setError(authError);
      throw authError;
    } finally {
      setIsLoading(false);
    }
  };

  const signup = async (request: SignupRequest) => {
    try {
      setIsLoading(true);
      setError(null);
      const userCredential = await auth().createUserWithEmailAndPassword(request.email, request.password);

      // Update profile with display name
      await userCredential.user.updateProfile({
        displayName: request.displayName
      });

      setUser(convertFirebaseUser(userCredential.user));
    } catch (error: any) {
      const authError = handleAuthError(error);
      setError(authError);
      throw authError;
    } finally {
      setIsLoading(false);
    }
  };

  const logout = async () => {
    try {
      setIsLoading(true);
      await auth().signOut();
      setUser(null);
    } catch (error: any) {
      const authError = handleAuthError(error);
      setError(authError);
      throw authError;
    } finally {
      setIsLoading(false);
    }
  };

  const loginWithSSO = async (provider: SSOProvider) => {
    try {
      setIsLoading(true);
      setError(null);

      switch (provider) {
        case 'google':
          // Check if your device supports Google Play
          await GoogleSignin.hasPlayServices({ showPlayServicesUpdateDialog: true });

          // Get the users ID token
          const { idToken } = await GoogleSignin.signIn();

          // Create a Google credential with the token
          const googleCredential = auth.GoogleAuthProvider.credential(idToken);

          // Sign-in the user with the credential
          const userCredential = await auth().signInWithCredential(googleCredential);
          setUser(convertFirebaseUser(userCredential.user));
          break;

        case 'facebook':
          throw new Error('Facebook login requires additional setup with @react-native-facebook/login-kit');

        default:
          throw new Error(`SSO provider ${provider} not supported`);
      }

    } catch (error: any) {
      const authError = handleAuthError(error);
      setError(authError);
      throw authError;
    } finally {
      setIsLoading(false);
    }
  };

  const resetPassword = async (email: string) => {
    try {
      setError(null);
      await sendPasswordResetEmail(auth, email);
    } catch (error: any) {
      const authError = handleAuthError(error);
      setError(authError);
      throw authError;
    }
  };

  const updateUserProfile = async (displayName: string, photoURL?: string) => {
    try {
      if (!auth.currentUser) throw new Error('No authenticated user');
      
      await updateProfile(auth.currentUser, {
        displayName,
        ...(photoURL && { photoURL })
      });
      
      setUser(convertFirebaseUser(auth.currentUser));
    } catch (error: any) {
      const authError = handleAuthError(error);
      setError(authError);
      throw authError;
    }
  };

  const changePassword = async (currentPassword: string, newPassword: string) => {
    try {
      if (!auth.currentUser || !auth.currentUser.email) {
        throw new Error('No authenticated user');
      }

      // Re-authenticate user before changing password
      const credential = EmailAuthProvider.credential(auth.currentUser.email, currentPassword);
      await reauthenticateWithCredential(auth.currentUser, credential);
      
      // Update password
      await updatePassword(auth.currentUser, newPassword);
    } catch (error: any) {
      const authError = handleAuthError(error);
      setError(authError);
      throw authError;
    }
  };

  // Listen for authentication state changes
  useEffect(() => {
    const unsubscribe = onAuthStateChanged(auth, (firebaseUser) => {
      if (firebaseUser) {
        setUser(convertFirebaseUser(firebaseUser));
      } else {
        setUser(null);
      }
      setIsLoading(false);
    });

    return unsubscribe;
  }, []);

  const value: AuthContextType = {
    user,
    isLoading,
    isAuthenticated,
    error,
    login,
    signup,
    logout,
    loginWithSSO,
    resetPassword,
    updateUserProfile,
    changePassword,
    clearError,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};
