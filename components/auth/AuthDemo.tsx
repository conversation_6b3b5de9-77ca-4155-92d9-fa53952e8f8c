import React, { useState } from 'react';
import { View, StyleSheet, Alert } from 'react-native';
import { Button, Card, Text, TextInput, Divider } from 'react-native-paper';
import { useAuth } from '../../contexts/AuthContext';

/**
 * Demo component showing how to use the authentication system
 * This can be used for testing or as a reference for implementation
 */
export default function AuthDemo() {
  const [email, setEmail] = useState('<EMAIL>');
  const [password, setPassword] = useState('password123');
  const [displayName, setDisplayName] = useState('Test User');

  const { 
    user, 
    isLoading, 
    isAuthenticated, 
    login, 
    signup, 
    logout, 
    resetPassword,
    error,
    clearError 
  } = useAuth();

  const handleLogin = async () => {
    try {
      clearError();
      await login({ email, password });
      Alert.alert('Success', 'Logged in successfully!');
    } catch (error: any) {
      Alert.alert('Login Failed', error.message);
    }
  };

  const handleSignup = async () => {
    try {
      clearError();
      await signup({ email, password, displayName });
      Alert.alert('Success', 'Account created successfully!');
    } catch (error: any) {
      Alert.alert('Signup Failed', error.message);
    }
  };

  const handleLogout = async () => {
    try {
      await logout();
      Alert.alert('Success', 'Logged out successfully!');
    } catch (error: any) {
      Alert.alert('Logout Failed', error.message);
    }
  };

  const handleResetPassword = async () => {
    try {
      await resetPassword(email);
      Alert.alert('Success', 'Password reset email sent!');
    } catch (error: any) {
      Alert.alert('Reset Failed', error.message);
    }
  };

  return (
    <View style={styles.container}>
      <Card style={styles.card}>
        <Card.Content>
          <Text variant="headlineSmall" style={styles.title}>
            Authentication Demo
          </Text>
          
          {error && (
            <Text style={styles.errorText}>
              Error: {error.message}
            </Text>
          )}

          {isAuthenticated ? (
            <View>
              <Text variant="titleMedium" style={styles.welcomeText}>
                Welcome, {user?.displayName || user?.email}!
              </Text>
              <Text variant="bodyMedium" style={styles.userInfo}>
                Email: {user?.email}
              </Text>
              <Text variant="bodyMedium" style={styles.userInfo}>
                UID: {user?.uid}
              </Text>
              <Text variant="bodyMedium" style={styles.userInfo}>
                Email Verified: {user?.emailVerified ? 'Yes' : 'No'}
              </Text>
              
              <Button 
                mode="contained" 
                onPress={handleLogout}
                style={styles.button}
                loading={isLoading}
              >
                Logout
              </Button>
            </View>
          ) : (
            <View>
              <TextInput
                label="Display Name"
                value={displayName}
                onChangeText={setDisplayName}
                mode="outlined"
                style={styles.input}
              />
              
              <TextInput
                label="Email"
                value={email}
                onChangeText={setEmail}
                mode="outlined"
                style={styles.input}
                keyboardType="email-address"
                autoCapitalize="none"
              />
              
              <TextInput
                label="Password"
                value={password}
                onChangeText={setPassword}
                mode="outlined"
                style={styles.input}
                secureTextEntry
              />

              <Button 
                mode="contained" 
                onPress={handleLogin}
                style={styles.button}
                loading={isLoading}
              >
                Login
              </Button>

              <Button 
                mode="outlined" 
                onPress={handleSignup}
                style={styles.button}
                loading={isLoading}
              >
                Sign Up
              </Button>

              <Divider style={styles.divider} />

              <Button 
                mode="text" 
                onPress={handleResetPassword}
                style={styles.button}
                loading={isLoading}
              >
                Reset Password
              </Button>
            </View>
          )}
        </Card.Content>
      </Card>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
    justifyContent: 'center',
  },
  card: {
    elevation: 4,
  },
  title: {
    textAlign: 'center',
    marginBottom: 24,
    fontWeight: 'bold',
  },
  input: {
    marginBottom: 16,
  },
  button: {
    marginBottom: 12,
  },
  divider: {
    marginVertical: 16,
  },
  errorText: {
    color: 'red',
    textAlign: 'center',
    marginBottom: 16,
  },
  welcomeText: {
    textAlign: 'center',
    marginBottom: 16,
    fontWeight: 'bold',
  },
  userInfo: {
    marginBottom: 8,
  },
});
