# AI Avatar Generator

A React Native application built with Expo for creating AI avatars and generating personalized images. This MVP allows users to create multiple AI avatars by uploading photos and generate custom images using those avatars.

## Features

### 🎭 Avatar Management
- **Multi-Step Avatar Creation**: Two workflows - upload images or use configurator with gender/trait selection
- **Advanced Training System**: AI avatar training with real-time progress tracking and status indicators
- **Avatar Gallery**: Modern grid layout with FlashList for optimal performance and smooth scrolling
- **Character Profiles**: Full-screen banner design with character details, stats, and image galleries
- **Theme Support**: Complete light/dark mode integration across all avatar screens

### 🖼️ Image Generation
- **Modern Generation Interface**: Large prompt input with character counter and aspect ratio selection
- **Aspect Ratio Options**: 6 different ratios (1:1, 9:16, 16:9, 3:2, 4:3, 5:4) for various use cases
- **FlashList Integration**: Efficient scrolling with form header and generated images grid below
- **Visual Feedback**: Loading states, progress indicators, and comprehensive success/error handling
- **Prompt Suggestions**: Inspiration and photo upload options for enhanced creativity

### 👤 User Profile & Settings
- **Statistics Dashboard**: Track avatar count, generated images, and training status with visual cards
- **Theme Toggle**: Switch between light, dark, and system default themes
- **Recent Activity**: View recently created avatars and their current status
- **Settings Management**: Organized settings with proper navigation and feedback

### 📱 User Interface & Navigation
- **Header-Free Design**: Clean, immersive experience with disabled headers across all screens
- **Floating Action Buttons**: Modern floating generate button for primary actions
- **Contextual Menus**: Hidden destructive actions (delete) in overflow menus for safety
- **Bottom Tab Navigation**: Streamlined access to Avatars and Profile sections
- **Material Design 3**: Modern UI components with proper theming, elevation, and animations

### 🚀 Performance Optimizations
- **FlashList Implementation**: Replaced all FlatList/ScrollView with FlashList for 60fps scrolling
- **Optimized Rendering**: Efficient item rendering with estimated sizes and proper virtualization
- **Memory Management**: Proper image loading and caching with expo-image for better performance
- **Responsive Design**: Adaptive layouts that work seamlessly across different screen sizes

## Technology Stack

- **Framework**: Expo SDK 53 with React Native
- **Navigation**: Expo Router with bottom tabs and stack navigation
- **UI Library**: React Native Paper (Material Design 3) with custom theming
- **Performance**: FlashList for optimized list rendering and scrolling
- **State Management**: TanStack Query (React Query) for API state and caching
- **Image Handling**: Expo Image Picker and Expo Image for optimal performance
- **Theming**: Custom theme context with light/dark mode support
- **TypeScript**: Full type safety throughout the application with strict mode

## Project Structure

```
app/
├── (tabs)/                 # Tab navigation screens
│   ├── _layout.tsx        # Tab layout configuration
│   ├── avatars.tsx        # Avatar management screen
│   └── profile.tsx        # User profile screen
├── avatar/[id].tsx        # Avatar detail screen
├── create-avatar.tsx      # Avatar creation screen
├── generate/[id].tsx      # Image generation screen
├── gallery/[id].tsx       # Generated images gallery
├── login.tsx             # Authentication screen
├── _layout.tsx           # Root layout with providers
└── index.tsx             # App entry point with auth routing

hooks/
├── useAvatars.ts         # Avatar management hooks
├── useImageGeneration.ts # Image generation hooks
└── useAuth.ts           # Authentication hooks with TanStack Query

services/
└── mockApi.ts           # Mock API implementation

types/
└── index.ts             # TypeScript type definitions

utils/
└── imageUtils.ts        # Image processing utilities
```

## Installation & Setup

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd characterise
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Start the development server**
   ```bash
   npx expo start
   ```

4. **Run on device/simulator**
   - **iOS**: Press `i` in the terminal or scan QR code with Camera app
   - **Android**: Press `a` in the terminal or scan QR code with Expo Go app
   - **Web**: Press `w` in the terminal

## Development Steps Taken

### Phase 1: Project Setup
- ✅ Initialized Expo project with TypeScript
- ✅ Installed React Native Paper for Material Design UI
- ✅ Added TanStack Query for API state management
- ✅ Set up Expo Image Picker for photo selection
- ✅ Configured project structure with proper TypeScript types

### Phase 2: Navigation & Layout
- ✅ Implemented bottom tab navigation using Expo Router
- ✅ Created tab layout with Avatars and Profile sections
- ✅ Set up proper navigation between screens
- ✅ Added Material Design theming throughout the app

### Phase 3: Avatar Management
- ✅ Built avatar creation screen with image upload
- ✅ Implemented avatar listing with training status
- ✅ Added avatar detail view with management options
- ✅ Created delete functionality with confirmation dialogs

### Phase 4: Image Processing
- ✅ Integrated Expo Image Picker with multi-selection
- ✅ Added image validation (5-10 images required)
- ✅ Created utility functions for image handling
- ✅ Implemented proper error handling and user feedback

### Phase 5: Image Generation
- ✅ Built image generation interface with custom prompts
- ✅ Added style selection (Realistic, Artistic, Cartoon, Professional)
- ✅ Implemented prompt suggestions for better UX
- ✅ Created image gallery for viewing generated content

### Phase 6: User Profile
- ✅ Developed profile screen with statistics dashboard
- ✅ Added recent activity tracking
- ✅ Implemented settings placeholders for future features
- ✅ Created proper loading and empty states

### Phase 7: Authentication System
- ✅ Implemented Firebase Authentication with email/password
- ✅ Added comprehensive login/signup screen with validation
- ✅ Created authentication context with session persistence
- ✅ Integrated SSO providers (Google, Facebook) - requires setup
- ✅ Added protected routes and authentication flow
- ✅ Updated profile screen with user info and logout functionality
- ✅ Created authentication hooks for TanStack Query integration

## Mock API Implementation

The app currently uses a mock API (`services/mockApi.ts`) that simulates:

- **Avatar Creation**: Simulates LoRA training with progress updates
- **Image Generation**: Returns placeholder images with realistic delays
- **Data Persistence**: Uses in-memory storage for the session
- **Error Handling**: Includes proper error responses and loading states

### API Endpoints Simulated:
- `GET /avatars` - Fetch user's avatars
- `POST /avatars` - Create new avatar
- `DELETE /avatars/:id` - Delete avatar
- `POST /generate` - Generate image from avatar
- `GET /images/:avatarId?` - Fetch generated images

## Future Integration

The mock API can be easily replaced with real backend services:

1. **Replace mock functions** in `services/mockApi.ts`
2. **Update API endpoints** to point to your backend
3. **Add authentication** using Expo AuthSession
4. **Implement real image upload** to cloud storage
5. **Connect to actual AI/ML services** for avatar training and generation

## Key Features for Production

### Ready for Backend Integration:
- ✅ Proper API abstraction layer
- ✅ React Query for caching and synchronization
- ✅ TypeScript interfaces for all data models
- ✅ Error handling and loading states
- ✅ Image upload and validation utilities

### Recommended Next Steps:
1. ✅ **Authentication System**: Firebase Auth with email/password and SSO
2. **Cloud Storage**: Implement image upload to AWS S3/Cloudinary
3. **Real AI Integration**: Connect to Stable Diffusion or similar APIs
4. **Push Notifications**: Notify users when training completes
5. **Payment Integration**: Add subscription/credit system
6. **Social Features**: Share generated images
7. **Advanced Editing**: Add image editing capabilities

## Running the App

### Prerequisites
1. **Install dependencies**
   ```bash
   npm install
   ```

2. **Configure Firebase Authentication**
   - Follow the setup guide in `FIREBASE_SETUP.md`
   - Update `config/firebase.ts` with your Firebase configuration
   - Enable Email/Password and desired SSO providers in Firebase Console

### Development
```bash
npm start          # Start Expo development server
npm run android    # Run on Android emulator
npm run ios        # Run on iOS simulator
npm run web        # Run in web browser
```

### Building for Production
```bash
npx expo build:android    # Build Android APK
npx expo build:ios        # Build iOS IPA
```

## Authentication Features

The app includes a complete Firebase Authentication system:

### ✅ Implemented Features:
- **Email/Password Authentication**: Secure login and signup with validation
- **Password Reset**: Email-based password recovery
- **Session Persistence**: Users stay logged in across app restarts
- **Protected Routes**: Automatic redirection based on authentication state
- **User Profile**: Display user information and logout functionality
- **SSO Ready**: Google and Facebook login (requires additional Firebase setup)
- **Error Handling**: Comprehensive error messages and validation

### 🔧 How to Use:
1. **Login Screen**: Navigate to `/login` or get redirected automatically when not authenticated
2. **Create Account**: Switch to signup mode and create a new account with email/password
3. **Access Protected Content**: Authenticated users can access all avatar features
4. **Profile Management**: View user info and logout from the Profile tab
5. **Password Reset**: Use "Forgot Password" link on login screen

### 🔐 Security Features:
- Firebase Authentication handles all security aspects
- Passwords are never stored locally
- JWT tokens are managed automatically
- Email verification support (can be enabled in Firebase)
- Secure session management with AsyncStorage persistence

## Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## License

This project is licensed under the MIT License - see the LICENSE file for details.

---

**Note**: This is an MVP (Minimum Viable Product) focused on core functionality. The UI is intentionally simple and can be enhanced in future iterations based on user feedback and requirements.
