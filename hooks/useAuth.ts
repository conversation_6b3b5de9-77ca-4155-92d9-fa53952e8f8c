import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { useAuth as useAuthContext } from '../contexts/AuthContext';
import { LoginRequest, SignupRequest, SSOProvider } from '../types';

// Custom hooks that integrate Firebase Auth with TanStack Query
export const useAuthQuery = () => {
  const { user, isLoading, isAuthenticated, error } = useAuthContext();
  
  return useQuery({
    queryKey: ['auth', 'user'],
    queryFn: () => Promise.resolve(user),
    enabled: false, // We don't need to fetch, just use the context state
    initialData: user,
    staleTime: Infinity, // Auth state is managed by Firebase
  });
};

export const useLoginMutation = () => {
  const { login } = useAuthContext();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (request: LoginRequest) => login(request),
    onSuccess: (data) => {
      // Invalidate and refetch user-related queries
      queryClient.invalidateQueries({ queryKey: ['auth'] });
      queryClient.invalidateQueries({ queryKey: ['avatars'] });
      queryClient.invalidateQueries({ queryKey: ['generatedImages'] });
    },
  });
};

export const useSignupMutation = () => {
  const { signup } = useAuthContext();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (request: SignupRequest) => signup(request),
    onSuccess: (data) => {
      // Invalidate and refetch user-related queries
      queryClient.invalidateQueries({ queryKey: ['auth'] });
    },
  });
};

export const useLogoutMutation = () => {
  const { logout } = useAuthContext();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: () => logout(),
    onSuccess: () => {
      // Clear all cached data on logout
      queryClient.clear();
    },
  });
};

export const useSSOLoginMutation = () => {
  const { loginWithSSO } = useAuthContext();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (provider: SSOProvider) => loginWithSSO(provider),
    onSuccess: (data) => {
      // Invalidate and refetch user-related queries
      queryClient.invalidateQueries({ queryKey: ['auth'] });
      queryClient.invalidateQueries({ queryKey: ['avatars'] });
      queryClient.invalidateQueries({ queryKey: ['generatedImages'] });
    },
  });
};

export const useResetPasswordMutation = () => {
  const { resetPassword } = useAuthContext();

  return useMutation({
    mutationFn: (email: string) => resetPassword(email),
  });
};

export const useUpdateProfileMutation = () => {
  const { updateUserProfile } = useAuthContext();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ displayName, photoURL }: { displayName: string; photoURL?: string }) => 
      updateUserProfile(displayName, photoURL),
    onSuccess: () => {
      // Invalidate auth queries to refresh user data
      queryClient.invalidateQueries({ queryKey: ['auth'] });
    },
  });
};

export const useChangePasswordMutation = () => {
  const { changePassword } = useAuthContext();

  return useMutation({
    mutationFn: ({ currentPassword, newPassword }: { currentPassword: string; newPassword: string }) => 
      changePassword(currentPassword, newPassword),
  });
};

// Re-export the main auth hook for convenience
export { useAuth } from '../contexts/AuthContext';
