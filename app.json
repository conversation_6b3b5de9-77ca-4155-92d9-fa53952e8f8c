{"expo": {"name": "characterise", "slug": "characterise", "version": "1.0.0", "orientation": "portrait", "icon": "./assets/images/icon.png", "scheme": "characterise", "userInterfaceStyle": "automatic", "newArchEnabled": true, "ios": {"supportsTablet": true}, "android": {"googleServicesFile": "./google-services.json", "package": "com.pingcube.charcaterise", "adaptiveIcon": {"foregroundImage": "./assets/images/adaptive-icon.png", "backgroundColor": "#ffffff"}, "edgeToEdgeEnabled": true}, "web": {"bundler": "metro", "output": "static", "favicon": "./assets/images/favicon.png"}, "plugins": ["expo-router", "@react-native-firebase/app", "@react-native-firebase/auth", ["expo-splash-screen", {"image": "./assets/images/splash-icon.png", "imageWidth": 200, "resizeMode": "contain", "backgroundColor": "#ffffff"}]], "experiments": {"typedRoutes": true}, "extra": {"eas": {"projectId": "2ac3d112-e9e3-471d-b947-bedb718cccbf"}}}}