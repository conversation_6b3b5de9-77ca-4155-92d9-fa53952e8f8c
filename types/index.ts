export interface Avatar {
  id: string;
  name: string;
  description?: string;
  images: string[]; // Array of image URIs
  createdAt: Date;
  isTraining: boolean;
  trainingProgress?: number;
}

export interface GeneratedImage {
  id: string;
  avatarId: string;
  imageUri: string;
  prompt: string;
  createdAt: Date;
}

export interface User {
  id: string;
  name: string;
  email: string;
  photoURL?: string;
  avatars: Avatar[];
  generatedImages: GeneratedImage[];
  createdAt: Date;
  lastLoginAt: Date;
}

export interface CreateAvatarRequest {
  name: string;
  description?: string;
  images: string[];
}

export interface GenerateImageRequest {
  avatarId: string;
  prompt: string;
  style?: string;
}

export interface ApiResponse<T> {
  data: T;
  success: boolean;
  message?: string;
}

// Authentication types
export interface AuthUser {
  uid: string;
  email: string | null;
  displayName: string | null;
  photoURL: string | null;
  emailVerified: boolean;
}

export interface LoginRequest {
  email: string;
  password: string;
}

export interface SignupRequest {
  email: string;
  password: string;
  displayName: string;
}

export interface AuthState {
  user: AuthUser | null;
  isLoading: boolean;
  isAuthenticated: boolean;
}

export type SSOProvider = 'google' | 'facebook' | 'apple';

export interface AuthError {
  code: string;
  message: string;
}
